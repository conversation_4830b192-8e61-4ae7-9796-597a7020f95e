#map {
  height: calc(100vh - 120px);
  width: 100%;
}

.modal-overlay {
  animation: fadeIn 0.3s ease-out;
}

.modal-content {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Custom marker pulse effect */
.pulse-dot {
  position: relative;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #3b82f6;
  animation: pulse-animation 1.5s infinite;
}

.pulse-dot::before {
  content: "";
  display: inline-block;
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: inherit;
  opacity: 0.7;
  animation: pulse-animation 1.5s infinite;
  animation-delay: 0.5s;
}

@keyframes pulse-animation {
  0% {
    transform: scale(0.6);
  }
  50% {
    transform: scale(1.2);
    box-shadow: 0 0 0 5px rgba(59, 130, 246, 0.2);
  }
  100% {
    transform: scale(0.6);
  }
}
