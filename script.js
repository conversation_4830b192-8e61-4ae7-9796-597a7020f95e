// Initialisation de Supabase
const supabaseUrl = "https://wlrrruemchacgyypexsu.supabase.co";
const supabaseKey =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndscnJydWVtY2hhY2d5eXBleHN1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzU2NjE4NzQsImV4cCI6MjA1MTIzNzg3NH0.MyGGVKey";
const supabase = supabase.createClient(supabaseUrl, supabaseKey);

// DOM Elements
const welcomeModal = document.getElementById("welcomeModal");
const arrivalModal = document.getElementById("arrivalModal");
const blockSelect = document.getElementById("blockSelect");
const lotSelect = document.getElementById("lotSelect");
const startNavigationBtn = document.getElementById("startNavigationBtn");
const changeDestinationBtn = document.getElementById("changeDestinationBtn");
const newDestinationBtn = document.getElementById("newDestinationBtn");
const exitNavigationBtn = document.getElementById("exitNavigationBtn");
const loadingSpinner = document.getElementById("loadingSpinner");
const statusText = document.getElementById("statusText");
const toast = document.getElementById("toast");
const toastMessage = document.getElementById("toastMessage");
const toastIcon = document.getElementById("toastIcon");
const compassBtn = document.getElementById("compassBtn");
const locationBtn = document.getElementById("locationBtn");
const zoomInBtn = document.getElementById("zoomInBtn");
const zoomOutBtn = document.getElementById("zoomOutBtn");

// Data will be loaded from Supabase
let lotData = {};
let allLocations = [];

const exitLocation = { lat: 14.347872973134175, lng: 120.95134859887523 };

// App state
let map;
let currentLocationWatchId = null;
let currentDestination = null;
let isNavigating = false;
let compassHeading = 0;

// Initialize the app
document.addEventListener("DOMContentLoaded", async () => {
  initEventListeners();
  showLoading("Chargement des données...");

  try {
    await loadAllLocations();
    await initMap();
    hideLoading();
    showWelcomeModal();
  } catch (error) {
    hideLoading();
    showToast("Erreur lors du chargement des données", "error");
    console.error("Initialization error:", error);
  }

  registerServiceWorker();
});

function initEventListeners() {
  // Block selection changes
  blockSelect.addEventListener("change", (e) => {
    const block = e.target.value;
    if (block) {
      populateLotSelect(block);
      lotSelect.disabled = false;
      checkEnableStartButton();
    } else {
      lotSelect.innerHTML = '<option value="">Select Lot</option>';
      lotSelect.disabled = true;
      startNavigationBtn.disabled = true;
    }
  });

  // Lot selection changes
  lotSelect.addEventListener("change", checkEnableStartButton);

  // Start navigation button
  startNavigationBtn.addEventListener("click", () => {
    const block = blockSelect.value;
    const lot = lotSelect.value;
    currentDestination = { block, lot };

    fetchCoordinatesFromSupabase(block, lot)
      .then((destination) => {
        hideWelcomeModal();
        showLoading("Calculating route...");

        // Store the destination data
        currentDestination = destination;

        // Simulate navigation (in real app, this would use actual GPS)
        setTimeout(() => {
          hideLoading();
          statusText.textContent = `Navigating to Block ${block}, Lot ${lot}`;
          showToast(`Navigation started to Block ${block}, Lot ${lot}`);
          isNavigating = true;

          // Center map on destination
          if (destination.coords && destination.coords.length >= 2) {
            const [lng, lat] = destination.coords;
            map.flyTo([lat, lng], 18);
          }

          // In a real app, we would use watchUserPosition() here
          // For demo, we'll simulate arrival after some time
          setTimeout(() => {
            showArrivalModal(block, lot);
          }, 3000);
        }, 1500);
      })
      .catch((err) => {
        hideLoading();
        showToast("Erreur lors de la récupération des coordonnées", "error");
        console.error(err);
      });
  });

  // Change destination button
  changeDestinationBtn.addEventListener("click", showWelcomeModal);

  // New destination from arrival modal
  newDestinationBtn.addEventListener("click", () => {
    hideArrivalModal();
    showWelcomeModal();
  });

  // Exit navigation button
  exitNavigationBtn.addEventListener("click", () => {
    hideArrivalModal();
    showLoading("Calculating route to exit...");
    currentDestination = { block: "EXIT", lot: "GATE" };

    setTimeout(() => {
      hideLoading();
      statusText.textContent = `Navigating to Exit Gate`;
      showToast(`Navigation started to Exit Gate`);
      isNavigating = true;

      // Simulate arrival at exit
      setTimeout(() => {
        showToast("You have reached the exit gate", "success");
        statusText.textContent = "Ready for navigation";
        isNavigating = false;
        currentDestination = null;
      }, 3000);
    }, 1500);
  });

  // Compass button
  compassBtn.addEventListener("click", () => {
    if (window.DeviceOrientationEvent) {
      showToast("Compass activated - rotate your device");
      window.addEventListener("deviceorientation", handleOrientation);
    } else {
      showToast("Compass not supported on this device", "error");
    }
  });

  // Location button
  locationBtn.addEventListener("click", () => {
    if (map && navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          map.flyTo([longitude, latitude], 18);
          showToast("Centered map to your location");
        },
        (error) => {
          showToast("Could not get your location", "error");
        }
      );
    }
  });

  // Zoom controls
  zoomInBtn.addEventListener("click", () => {
    if (map) {
      map.setZoom(map.getZoom() + 1);
    }
  });

  zoomOutBtn.addEventListener("click", () => {
    if (map) {
      map.setZoom(map.getZoom() - 1);
    }
  });
}

async function loadAllLocations() {
  try {
    const { data, error } = await supabase
      .from("locations")
      .select("*")
      .order("block")
      .order("lot");

    if (error) throw error;

    allLocations = data || [];

    // Organiser les données par bloc
    lotData = {};
    allLocations.forEach((location) => {
      if (!lotData[location.block]) {
        lotData[location.block] = [];
      }
      lotData[location.block].push(location.lot);
    });

    // Populate block select
    populateBlockSelect();
  } catch (error) {
    console.error("Error loading locations:", error);
    throw error;
  }
}

function populateBlockSelect() {
  const blocks = Object.keys(lotData).sort();

  // Clear existing options except the first one
  blockSelect.innerHTML = '<option value="">Select Block</option>';

  blocks.forEach((block) => {
    const option = document.createElement("option");
    option.value = block;
    option.textContent = `Block ${block}`;
    blockSelect.appendChild(option);
  });
}

function initMap() {
  // Initialize map with a default center (will be adjusted based on data)
  map = L.map("map").setView([14.347872973134175, 120.95134859887523], 15);

  L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
    attribution:
      '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
  }).addTo(map);

  // Add markers for all locations from Supabase
  allLocations.forEach((location) => {
    if (location.coordinates && location.coordinates.coordinates) {
      const [lng, lat] = location.coordinates.coordinates;
      const marker = L.marker([lat, lng]).addTo(map);
      marker.bindPopup(`<b>Block ${location.block}, Lot ${location.lot}</b>`);

      // Add a circle for better visibility
      L.circle([lat, lng], {
        color: "#3B82F6",
        fillColor: "#3B82F688",
        fillOpacity: 0.5,
        radius: 20,
      })
        .addTo(map)
        .bindTooltip(`Block ${location.block}, Lot ${location.lot}`, {
          permanent: false,
          direction: "top",
        });
    }
  });

  // Adjust map view to fit all markers if we have locations
  if (allLocations.length > 0) {
    const markers = [];
    allLocations.forEach((location) => {
      if (location.coordinates && location.coordinates.coordinates) {
        const [lng, lat] = location.coordinates.coordinates;
        markers.push([lat, lng]);
      }
    });

    if (markers.length > 0) {
      const group = new L.featureGroup(
        markers.map((coords) => L.marker(coords))
      );
      map.fitBounds(group.getBounds().pad(0.1));
    }
  }

  // Add exit marker (you might want to add this to your database too)
  const exitLocation = { lat: 14.347872973134175, lng: 120.95134859887523 };
  const exitMarker = L.marker([exitLocation.lat, exitLocation.lng]).addTo(map);
  exitMarker.bindPopup("<b>Exit Gate</b>");
  L.circle([exitLocation.lat, exitLocation.lng], {
    color: "#EF4444",
    fillColor: "#EF444488",
    fillOpacity: 0.5,
    radius: 20,
  })
    .addTo(map)
    .bindTooltip("Exit Gate", { permanent: false, direction: "top" });
}

function populateLotSelect(block) {
  lotSelect.innerHTML = '<option value="">Select Lot</option>';
  const lots = lotData[block] || [];

  lots.forEach((lot) => {
    const option = document.createElement("option");
    option.value = lot;
    option.textContent = `Lot ${lot}`;
    lotSelect.appendChild(option);
  });
}

function checkEnableStartButton() {
  startNavigationBtn.disabled = !(blockSelect.value && lotSelect.value);
}

function showWelcomeModal() {
  welcomeModal.classList.remove("hidden");
}

function hideWelcomeModal() {
  welcomeModal.classList.add("hidden");
}

function showArrivalModal(block, lot) {
  isNavigating = false;
  statusText.textContent = `Arrived at Block ${block}, Lot ${lot}`;
  arrivalLocationText.textContent = `Block ${block}, Lot ${lot}`;
  arrivalModal.classList.remove("hidden");
}

function hideArrivalModal() {
  arrivalModal.classList.add("hidden");
}

function showLoading(message = "Loading...") {
  statusText.textContent = message;
  loadingSpinner.classList.remove("hidden");
}

function hideLoading() {
  loadingSpinner.classList.add("hidden");
}

function showToast(message, type = "info") {
  toastMessage.textContent = message;

  // Set icon and color based on type
  switch (type) {
    case "error":
      toast.className =
        "fixed bottom-20 left-1/2 transform -translate-x-1/2 bg-red-600 text-white px-4 py-2 rounded-md shadow-lg z-50 flex items-center space-x-2 transition-opacity duration-300";
      toastIcon.className = "fas fa-exclamation-circle";
      break;
    case "success":
      toast.className =
        "fixed bottom-20 left-1/2 transform -translate-x-1/2 bg-green-600 text-white px-4 py-2 rounded-md shadow-lg z-50 flex items-center space-x-2 transition-opacity duration-300";
      toastIcon.className = "fas fa-check-circle";
      break;
    default:
      toast.className =
        "fixed bottom-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-2 rounded-md shadow-lg z-50 flex items-center space-x-2 transition-opacity duration-300";
      toastIcon.className = "fas fa-info-circle";
  }

  toast.classList.remove("hidden");

  // Hide after 3 seconds
  setTimeout(() => {
    toast.classList.add("hidden");
  }, 3000);
}

function fetchCoordinatesFromSupabase(block, lot) {
  return new Promise(async (resolve, reject) => {
    try {
      const { data, error } = await supabase
        .from("locations")
        .select("*")
        .eq("block", block)
        .eq("lot", lot)
        .single();

      if (error || !data) throw error || new Error("Location introuvable");

      // Adapter la structure pour correspondre à votre format
      const destination = {
        coords: data.coordinates.coordinates, // [lng, lat] format GeoJSON
        data,
      };

      resolve(destination);
    } catch (err) {
      console.error("Supabase error:", err);
      reject(err);
    }
  });
}

function handleOrientation(event) {
  if (event.webkitCompassHeading) {
    // iOS non-standard way
    compassHeading = event.webkitCompassHeading;
  } else if (event.absolute && event.alpha !== null) {
    // Standard way
    compassHeading = 360 - event.alpha;
  }

  // In a real app, we would rotate the map based on compassHeading
  showToast(`Heading: ${Math.round(compassHeading)}°`, "info");
}

function registerServiceWorker() {
  if ("serviceWorker" in navigator) {
    window.addEventListener("load", () => {
      navigator.serviceWorker
        .register("/sw.js")
        .then((registration) => {
          console.log("ServiceWorker registration successful");
        })
        .catch((err) => {
          console.log("ServiceWorker registration failed: ", err);
        });
    });
  }
}
