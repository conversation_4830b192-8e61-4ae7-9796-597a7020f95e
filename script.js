// Initialisation de Supabase
const supabaseUrl = "VOTRE_URL_SUPABASE";
const supabaseKey = "VOTRE_CLE_ANON_SUPABASE";
const supabase = supabase.createClient(supabaseUrl, supabaseKey);

// DOM Elements
const welcomeModal = document.getElementById("welcomeModal");
const arrivalModal = document.getElementById("arrivalModal");
const blockSelect = document.getElementById("blockSelect");
const lotSelect = document.getElementById("lotSelect");
const startNavigationBtn = document.getElementById("startNavigationBtn");
const changeDestinationBtn = document.getElementById("changeDestinationBtn");
const newDestinationBtn = document.getElementById("newDestinationBtn");
const exitNavigationBtn = document.getElementById("exitNavigationBtn");
const loadingSpinner = document.getElementById("loadingSpinner");
const statusText = document.getElementById("statusText");
const toast = document.getElementById("toast");
const toastMessage = document.getElementById("toastMessage");
const toastIcon = document.getElementById("toastIcon");
const compassBtn = document.getElementById("compassBtn");
const locationBtn = document.getElementById("locationBtn");
const zoomInBtn = document.getElementById("zoomInBtn");
const zoomOutBtn = document.getElementById("zoomOutBtn");

// Mock data (in a real app, this would come from Supabase)
const lotData = {
  A: ["1", "2", "3", "4", "5"],
  B: ["10", "11", "12"],
  C: ["20", "21", "22", "23"],
  D: ["30", "31"],
};

const lotCoordinates = {
  A: {
    1: { lat: -6.2088, lng: 106.8456 },
    2: { lat: -6.2095, lng: 106.846 },
    3: { lat: -6.21, lng: 106.8465 },
    4: { lat: -6.2105, lng: 106.847 },
    5: { lat: -6.211, lng: 106.8475 },
  },
  B: {
    10: { lat: -6.208, lng: 106.844 },
    11: { lat: -6.2075, lng: 106.8435 },
    12: { lat: -6.207, lng: 106.843 },
  },
  C: {
    20: { lat: -6.212, lng: 106.848 },
    21: { lat: -6.2125, lng: 106.8485 },
    22: { lat: -6.213, lng: 106.849 },
    23: { lat: -6.2135, lng: 106.8495 },
  },
  D: {
    30: { lat: -6.214, lng: 106.85 },
    31: { lat: -6.2145, lng: 106.8505 },
  },
};

const exitLocation = { lat: -6.205, lng: 106.842 };

// App state
let map;
let currentLocationWatchId = null;
let currentDestination = null;
let isNavigating = false;
let compassHeading = 0;

// Initialize the app
document.addEventListener("DOMContentLoaded", () => {
  initEventListeners();
  initMap();
  showWelcomeModal();
  registerServiceWorker();
});

function initEventListeners() {
  // Block selection changes
  blockSelect.addEventListener("change", (e) => {
    const block = e.target.value;
    if (block) {
      populateLotSelect(block);
      lotSelect.disabled = false;
      checkEnableStartButton();
    } else {
      lotSelect.innerHTML = '<option value="">Select Lot</option>';
      lotSelect.disabled = true;
      startNavigationBtn.disabled = true;
    }
  });

  // Lot selection changes
  lotSelect.addEventListener("change", checkEnableStartButton);

  // Start navigation button
  startNavigationBtn.addEventListener("click", () => {
    const block = blockSelect.value;
    const lot = lotSelect.value;
    currentDestination = { block, lot };

    fetchCoordinatesFromSupabase(block, lot)
      .then((coords) => {
        hideWelcomeModal();
        showLoading("Calculating route...");

        // Simulate navigation (in real app, this would use actual GPS)
        setTimeout(() => {
          hideLoading();
          statusText.textContent = `Navigating to Block ${block}, Lot ${lot}`;
          showToast(`Navigation started to Block ${block}, Lot ${lot}`);
          isNavigating = true;

          // In a real app, we would use watchUserPosition() here
          // For demo, we'll simulate arrival after some time
          setTimeout(() => {
            showArrivalModal(block, lot);
          }, 3000);
        }, 1500);
      })
      .catch((err) => {
        hideLoading();
        showToast("Error fetching destination coordinates", "error");
        console.error(err);
      });
  });

  // Change destination button
  changeDestinationBtn.addEventListener("click", showWelcomeModal);

  // New destination from arrival modal
  newDestinationBtn.addEventListener("click", () => {
    hideArrivalModal();
    showWelcomeModal();
  });

  // Exit navigation button
  exitNavigationBtn.addEventListener("click", () => {
    hideArrivalModal();
    showLoading("Calculating route to exit...");
    currentDestination = { block: "EXIT", lot: "GATE" };

    setTimeout(() => {
      hideLoading();
      statusText.textContent = `Navigating to Exit Gate`;
      showToast(`Navigation started to Exit Gate`);
      isNavigating = true;

      // Simulate arrival at exit
      setTimeout(() => {
        showToast("You have reached the exit gate", "success");
        statusText.textContent = "Ready for navigation";
        isNavigating = false;
        currentDestination = null;
      }, 3000);
    }, 1500);
  });

  // Compass button
  compassBtn.addEventListener("click", () => {
    if (window.DeviceOrientationEvent) {
      showToast("Compass activated - rotate your device");
      window.addEventListener("deviceorientation", handleOrientation);
    } else {
      showToast("Compass not supported on this device", "error");
    }
  });

  // Location button
  locationBtn.addEventListener("click", () => {
    if (map && navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          map.flyTo([longitude, latitude], 18);
          showToast("Centered map to your location");
        },
        (error) => {
          showToast("Could not get your location", "error");
        }
      );
    }
  });

  // Zoom controls
  zoomInBtn.addEventListener("click", () => {
    if (map) {
      map.setZoom(map.getZoom() + 1);
    }
  });

  zoomOutBtn.addEventListener("click", () => {
    if (map) {
      map.setZoom(map.getZoom() - 1);
    }
  });
}

function initMap() {
  // Mock initialization with leaflet.js (in real app, would use OpenLayers)
  map = L.map("map").setView([120.95134859887523, 14.347872973134175], 15);

  L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
    attribution:
      '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
  }).addTo(map);

  // Add some mock houses for visualization
  Object.keys(lotCoordinates).forEach((block) => {
    Object.keys(lotCoordinates[block]).forEach((lot) => {
      const { lat, lng } = lotCoordinates[block][lot];
      const marker = L.marker([lat, lng]).addTo(map);
      marker.bindPopup(`<b>Block ${block}, Lot ${lot}</b>`);

      // Add a circle for better visibility
      L.circle([lat, lng], {
        color: "#3B82F6",
        fillColor: "#3B82F688",
        fillOpacity: 0.5,
        radius: 20,
      })
        .addTo(map)
        .bindTooltip(`Block ${block}, Lot ${lot}`, {
          permanent: false,
          direction: "top",
        });
    });
  });

  // Add exit marker
  const exitMarker = L.marker([exitLocation.lat, exitLocation.lng]).addTo(map);
  exitMarker.bindPopup("<b>Exit Gate</b>").openPopup();
  L.circle([exitLocation.lat, exitLocation.lng], {
    color: "#EF4444",
    fillColor: "#EF444488",
    fillOpacity: 0.5,
    radius: 20,
  })
    .addTo(map)
    .bindTooltip("Exit Gate", { permanent: false, direction: "top" });
}

function populateLotSelect(block) {
  lotSelect.innerHTML = '<option value="">Select Lot</option>';
  const lots = lotData[block] || [];

  lots.forEach((lot) => {
    const option = document.createElement("option");
    option.value = lot;
    option.textContent = `Lot ${lot}`;
    lotSelect.appendChild(option);
  });
}

function checkEnableStartButton() {
  startNavigationBtn.disabled = !(blockSelect.value && lotSelect.value);
}

function showWelcomeModal() {
  welcomeModal.classList.remove("hidden");
}

function hideWelcomeModal() {
  welcomeModal.classList.add("hidden");
}

function showArrivalModal(block, lot) {
  isNavigating = false;
  statusText.textContent = `Arrived at Block ${block}, Lot ${lot}`;
  arrivalLocationText.textContent = `Block ${block}, Lot ${lot}`;
  arrivalModal.classList.remove("hidden");
}

function hideArrivalModal() {
  arrivalModal.classList.add("hidden");
}

function showLoading(message = "Loading...") {
  statusText.textContent = message;
  loadingSpinner.classList.remove("hidden");
}

function hideLoading() {
  loadingSpinner.classList.add("hidden");
}

function showToast(message, type = "info") {
  toastMessage.textContent = message;

  // Set icon and color based on type
  switch (type) {
    case "error":
      toast.className =
        "fixed bottom-20 left-1/2 transform -translate-x-1/2 bg-red-600 text-white px-4 py-2 rounded-md shadow-lg z-50 flex items-center space-x-2 transition-opacity duration-300";
      toastIcon.className = "fas fa-exclamation-circle";
      break;
    case "success":
      toast.className =
        "fixed bottom-20 left-1/2 transform -translate-x-1/2 bg-green-600 text-white px-4 py-2 rounded-md shadow-lg z-50 flex items-center space-x-2 transition-opacity duration-300";
      toastIcon.className = "fas fa-check-circle";
      break;
    default:
      toast.className =
        "fixed bottom-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-2 rounded-md shadow-lg z-50 flex items-center space-x-2 transition-opacity duration-300";
      toastIcon.className = "fas fa-info-circle";
  }

  toast.classList.remove("hidden");

  // Hide after 3 seconds
  setTimeout(() => {
    toast.classList.add("hidden");
  }, 3000);
}

function fetchCoordinatesFromSupabase(block, lot) {
  return new Promise(async (resolve, reject) => {
    try {
      const { data, error } = await supabase
        .from("lots")
        .select("lat, lng")
        .eq("block", block)
        .eq("lot_number", lot)
        .single();

      if (error) throw error;

      if (data) {
        resolve(data);
      } else {
        reject(new Error("Coordinates not found for this block and lot"));
      }
    } catch (err) {
      console.error("Supabase error:", err);
      reject(err);
    }
  });
}

function handleOrientation(event) {
  if (event.webkitCompassHeading) {
    // iOS non-standard way
    compassHeading = event.webkitCompassHeading;
  } else if (event.absolute && event.alpha !== null) {
    // Standard way
    compassHeading = 360 - event.alpha;
  }

  // In a real app, we would rotate the map based on compassHeading
  showToast(`Heading: ${Math.round(compassHeading)}°`, "info");
}

function registerServiceWorker() {
  if ("serviceWorker" in navigator) {
    window.addEventListener("load", () => {
      navigator.serviceWorker
        .register("/sw.js")
        .then((registration) => {
          console.log("ServiceWorker registration successful");
        })
        .catch((err) => {
          console.log("ServiceWorker registration failed: ", err);
        });
    });
  }
}
