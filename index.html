<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Residential Navigation PWA</title>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2.50.0/dist/umd/supabase.min.js"></script>

    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
      integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
    <link rel="manifest" href="/manifest.json" />
    <link rel="stylesheet" href="styles.css" />
  </head>
  <body class="flex flex-col min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="bg-blue-600 text-white p-4 shadow-md">
      <div class="container mx-auto flex justify-between items-center">
        <div class="flex items-center space-x-2">
          <i class="fas fa-map-marked-alt text-2xl"></i>
          <h1 class="text-xl font-bold">Residential Navigation</h1>
        </div>
        <button
          id="locationBtn"
          class="p-2 rounded-full bg-blue-700 hover:bg-blue-800 transition"
        >
          <i class="fas fa-location-arrow"></i>
        </button>
      </div>
    </header>

    <!-- Main Map Area -->
    <main id="main-content" class="flex-1 relative">
      <div id="map" class="z-0"></div>

      <!-- Navigation Controls -->
      <div class="absolute bottom-20 right-4 z-10 flex flex-col space-y-2">
        <button
          id="zoomInBtn"
          class="p-3 bg-white rounded-full shadow-md hover:bg-gray-100 transition"
        >
          <i class="fas fa-plus text-gray-700"></i>
        </button>
        <button
          id="zoomOutBtn"
          class="p-3 bg-white rounded-full shadow-md hover:bg-gray-100 transition"
        >
          <i class="fas fa-minus text-gray-700"></i>
        </button>
        <button
          id="compassBtn"
          class="p-3 bg-white rounded-full shadow-md hover:bg-gray-100 transition"
        >
          <i class="fas fa-compass text-blue-600"></i>
        </button>
      </div>
    </main>

    <!-- Footer -->
    <footer
      class="bg-gray-800 text-white p-4 flex items-center justify-between"
    >
      <div class="text-sm">
        Status:
        <span id="statusText" class="font-medium">Ready for navigation</span>
      </div>
      <button
        id="changeDestinationBtn"
        class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition flex items-center space-x-2"
      >
        <i class="fas fa-directions"></i>
        <span>Change Destination</span>
      </button>
    </footer>

    <!-- Welcome Modal -->
    <div
      id="welcomeModal"
      class="modal-overlay fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4"
    >
      <div
        class="modal-content bg-white rounded-lg shadow-xl max-w-md w-full transform transition-all"
      >
        <div class="bg-blue-600 text-white p-4 rounded-t-lg">
          <h2 class="text-xl font-bold">Welcome to Residential Navigation</h2>
        </div>
        <div class="p-6">
          <p class="text-gray-700 mb-4">
            Please select your destination below to start navigation.
          </p>

          <div class="space-y-4">
            <div>
              <label
                for="blockSelect"
                class="block text-sm font-medium text-gray-700 mb-1"
                >Block Number</label
              >
              <select
                id="blockSelect"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select Block</option>
                <option value="A">Block A</option>
                <option value="B">Block B</option>
                <option value="C">Block C</option>
                <option value="D">Block D</option>
              </select>
            </div>

            <div>
              <label
                for="lotSelect"
                class="block text-sm font-medium text-gray-700 mb-1"
                >Lot Number</label
              >
              <select
                id="lotSelect"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled
              >
                <option value="">Select Lot</option>
              </select>
            </div>
          </div>

          <div class="mt-6 flex justify-end space-x-3">
            <button
              id="startNavigationBtn"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-gray-400 disabled:cursor-not-allowed"
              disabled
            >
              Start Navigation
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Arrival Modal -->
    <div
      id="arrivalModal"
      class="modal-overlay fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4 hidden"
    >
      <div
        class="modal-content bg-white rounded-lg shadow-xl max-w-md w-full transform transition-all"
      >
        <div class="bg-green-600 text-white p-4 rounded-t-lg">
          <h2 class="text-xl font-bold">Destination Reached!</h2>
        </div>
        <div class="p-6">
          <div class="flex items-center justify-center mb-4">
            <div class="pulse-dot"></div>
            <div class="ml-3 text-lg font-medium">
              You've arrived at:
              <span id="arrivalLocationText" class="font-bold"
                >Block A, Lot 5</span
              >
            </div>
          </div>

          <p class="text-gray-700 mb-6 text-center">
            What would you like to do next?
          </p>

          <div class="space-y-3">
            <button
              id="newDestinationBtn"
              class="w-full px-4 py-2 flex items-center justify-center space-x-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition"
            >
              <i class="fas fa-map-marker-alt"></i>
              <span>Choose New Destination</span>
            </button>

            <button
              id="exitNavigationBtn"
              class="w-full px-4 py-2 flex items-center justify-center space-x-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition"
            >
              <i class="fas fa-sign-out-alt"></i>
              <span>Navigate to Exit</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading Spinner -->
    <div
      id="loadingSpinner"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden"
    >
      <div
        class="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-600"
      ></div>
    </div>

    <!-- Notification Toast -->
    <div
      id="toast"
      class="fixed bottom-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-2 rounded-md shadow-lg z-50 hidden transition-opacity duration-300"
    >
      <div class="flex items-center space-x-2">
        <i id="toastIcon" class="fas fa-info-circle"></i>
        <span id="toastMessage">This is a notification</span>
      </div>
    </div>

    <script src="script.js"></script>
    <!-- Leaflet.js for map visualization (in real app would use OpenLayers) -->
    <link
      rel="stylesheet"
      href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
      integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
      crossorigin=""
    />
    <script
      src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
      integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
      crossorigin=""
    ></script>
  </body>
</html>
